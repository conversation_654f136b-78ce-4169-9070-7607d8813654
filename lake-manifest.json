{"version": "1.1.0", "packagesDir": ".lake/packages", "packages": [{"type": "path", "scope": "", "name": "mathlib", "manifestFile": "lake-manifest.json", "inherited": false, "dir": "/Users/<USER>/tools/lean/AAIProofs/.lake/packages/mathlib", "configFile": "lakefile.lean"}, {"url": "https://github.com/leanprover-community/plausible", "type": "git", "subDir": null, "scope": "leanprover-community", "rev": "240eddc1bb31420fbbc57fe5cc579435c2522493", "name": "plausible", "manifestFile": "lake-manifest.json", "inputRev": "main", "inherited": true, "configFile": "lakefile.toml"}, {"url": "https://github.com/leanprover-community/LeanSearchClient", "type": "git", "subDir": null, "scope": "leanprover-community", "rev": "99657ad92e23804e279f77ea6dbdeebaa1317b98", "name": "LeanSearchClient", "manifestFile": "lake-manifest.json", "inputRev": "main", "inherited": true, "configFile": "lakefile.toml"}, {"url": "https://github.com/leanprover-community/import-graph", "type": "git", "subDir": null, "scope": "leanprover-community", "rev": "7c02243c07b61d493d7607ede432026781a3e47c", "name": "importGraph", "manifestFile": "lake-manifest.json", "inputRev": "main", "inherited": true, "configFile": "lakefile.toml"}, {"url": "https://github.com/leanprover-community/ProofWidgets4", "type": "git", "subDir": null, "scope": "leanprover-community", "rev": "6e47cc88cfbf1601ab364e9a4de5f33f13401ff8", "name": "proofwidgets", "manifestFile": "lake-manifest.json", "inputRev": "v0.0.71", "inherited": true, "configFile": "lakefile.lean"}, {"url": "https://github.com/leanprover-community/aesop", "type": "git", "subDir": null, "scope": "leanprover-community", "rev": "3b779e9d1c73837a3764d516d81f942de391b6f0", "name": "aesop", "manifestFile": "lake-manifest.json", "inputRev": "master", "inherited": true, "configFile": "lakefile.toml"}, {"url": "https://github.com/leanprover-community/quote4", "type": "git", "subDir": null, "scope": "leanprover-community", "rev": "f85ad59c9b60647ef736719c23edd4578f723806", "name": "Qq", "manifestFile": "lake-manifest.json", "inputRev": "master", "inherited": true, "configFile": "lakefile.toml"}, {"url": "https://github.com/leanprover-community/batteries", "type": "git", "subDir": null, "scope": "leanprover-community", "rev": "a9a0cb7672b7134497c9d813e53999c9311f4037", "name": "batteries", "manifestFile": "lake-manifest.json", "inputRev": "main", "inherited": true, "configFile": "lakefile.toml"}, {"url": "https://github.com/leanprover/lean4-cli", "type": "git", "subDir": null, "scope": "leanprover", "rev": "cacb481a1eaa4d7d4530a27b606c60923da21caf", "name": "<PERSON><PERSON>", "manifestFile": "lake-manifest.json", "inputRev": "main", "inherited": true, "configFile": "lakefile.toml"}], "name": "AAIProofs", "lakeDir": ".lake"}