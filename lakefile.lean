import Lake
open Lake DSL

package «AAIProofs» where
  -- package-wide options (if any)

-- Use your vendored mathlib clone:
-- Note: local paths are written as strings separated by `/` in the DSL,
-- but a single string also works.
require mathlib from "/Users/<USER>/tools/lean/AAIProofs/.lake/packages/mathlib"

-- Library target (optional to mark default, but handy)
@[default_target] lean_lib «AAIProofs» where

-- Executable target (builds Main.lean)
@[default_target] lean_exe aaiproofs where
  root := `Main
