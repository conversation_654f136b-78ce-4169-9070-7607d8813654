import Mathlib.Topology.Basic
import Mathlib.Topology.Closure

open Set
noncomputable section
open Classical

namespace AAIProofs

/-- Kuratowski-style closure operator on a type `α`. -/
structure KuratowskiClosure (α : Type*) where
  cl               : Set α → Set α
  mono             : ∀ {A B : Set α}, A ⊆ B → cl A ⊆ cl B
  idempotent       : ∀ A, cl (cl A) = cl A
  inflationary     : ∀ A, A ⊆ cl A
  empty_closed     : cl (∅ : Set α) = ∅
  union_preserving : ∀ A B, cl (A ∪ B) = cl A ∪ cl B

namespace KuratowskiClosure

variable {α : Type*} (K : KuratowskiClosure α)

/-- Tiny derived lemma showing how to use the axioms. -/
lemma cl_union_subset (A B : Set α) :
    K.cl (A ∪ B) ⊆ K.cl A ∪ K.cl B := by
  simp [K.union_preserving A B]

end KuratowskiClosure

/-- The canonical Kuratowski closure induced by any topological space. -/
def TopologyClosure (α : Type*) [TopologicalSpace α] : KuratowskiClosure α where
  cl               := closure
  mono             := by
    intro A B hAB; exact closure_mono hAB
  idempotent       := by
    intro A; exact closure_closure
  inflationary     := by
    intro A; exact subset_closure
  empty_closed     := by
    exact closure_empty
  union_preserving := by
    intro A B; exact closure_union

end AAIProofs
